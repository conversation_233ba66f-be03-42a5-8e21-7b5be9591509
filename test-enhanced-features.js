#!/usr/bin/env node

/**
 * Enhanced Features Test for Kritrima AI
 * Tests all the newly implemented features and enhancements
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Simple color functions without chalk dependency
const colors = {
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
  gray: (text) => `\x1b[90m${text}\x1b[0m`
};

class EnhancedFeaturesTest {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const colorFuncs = {
      info: colors.blue,
      success: colors.green,
      error: colors.red,
      warning: colors.yellow
    };

    console.log(`${colorFuncs[type](`[${type.toUpperCase()}]`)} ${timestamp} - ${message}`);
  }

  async runTest(testName, testFunction) {
    this.log(`Running test: ${testName}`, 'info');

    try {
      await testFunction();
      this.log(`✅ Test passed: ${testName}`, 'success');
      this.testResults.push({ name: testName, status: 'PASSED' });
      this.passedTests++;
    } catch (error) {
      this.log(`❌ Test failed: ${testName} - ${error.message}`, 'error');
      this.testResults.push({ name: testName, status: 'FAILED', error: error.message });
      this.failedTests++;
    }
  }

  async testBuildStatus() {
    return new Promise((resolve, reject) => {
      const buildProcess = spawn('npm', ['run', 'build'], { stdio: 'pipe' });

      buildProcess.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Build failed with exit code ${code}`));
        }
      });

      buildProcess.on('error', (error) => {
        reject(new Error(`Build process error: ${error.message}`));
      });
    });
  }

  async testFileStructure() {
    const requiredFiles = [
      'src/cli/interface.ts',
      'src/models/manager.ts',
      'src/tools/manager.ts',
      'src/context/manager.ts',
      'src/core/executor.ts',
      'src/core/error-detector.ts',
      'src/core/diff-reviewer.ts',
      'src/types/index.ts',
      'src/index.ts',
      'package.json',
      'tsconfig.json'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`);
      }
    }
  }

  async testEnhancedCLIInterface() {
    const interfaceFile = 'src/cli/interface.ts';
    const content = fs.readFileSync(interfaceFile, 'utf8');

    // Check for enhanced features
    const requiredFeatures = [
      'displayHeader',
      'handleKeyPress',
      'handleDoubleEscape',
      'handleStreamingResponse',
      'handleErrorWithResilience',
      'loadContextWithProgress',
      'startCodebaseMonitoring',
      'applyTheme'
    ];

    for (const feature of requiredFeatures) {
      if (!content.includes(feature)) {
        throw new Error(`Enhanced CLI feature missing: ${feature}`);
      }
    }
  }

  async testToolsImplementation() {
    const toolsDir = 'src/tools';
    const requiredTools = [
      'shell.ts',
      'file.ts',
      'edit.ts',
      'write.ts',
      'grep.ts',
      'web.ts',
      'manager.ts'
    ];

    for (const tool of requiredTools) {
      const toolPath = path.join(toolsDir, tool);
      if (!fs.existsSync(toolPath)) {
        throw new Error(`Required tool missing: ${tool}`);
      }
    }
  }

  async testModelProviders() {
    const providersDir = 'src/models/providers';
    const requiredProviders = [
      'openai.ts',
      'anthropic.ts',
      'deepseek.ts',
      'ollama.ts'
    ];

    for (const provider of requiredProviders) {
      const providerPath = path.join(providersDir, provider);
      if (!fs.existsSync(providerPath)) {
        throw new Error(`Required provider missing: ${provider}`);
      }
    }
  }

  async testTypeDefinitions() {
    const typesFile = 'src/types/index.ts';
    const content = fs.readFileSync(typesFile, 'utf8');

    const requiredTypes = [
      'AIModel',
      'ModelConfig',
      'Message',
      'Tool',
      'ContextFile',
      'ChatSession',
      'ExecutionPlan',
      'StreamingResponse',
      'ErrorDetection',
      'CLIConfig'
    ];

    for (const type of requiredTypes) {
      if (!content.includes(`interface ${type}`)) {
        throw new Error(`Required type definition missing: ${type}`);
      }
    }
  }

  async testPackageConfiguration() {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

    const requiredDependencies = [
      '@anthropic-ai/sdk',
      'openai',
      'axios',
      'chalk',
      'commander',
      'inquirer',
      'ora',
      'readline',
      'ws',
      'yaml',
      'glob',
      'chokidar',
      'diff'
    ];

    for (const dep of requiredDependencies) {
      if (!packageJson.dependencies[dep]) {
        throw new Error(`Required dependency missing: ${dep}`);
      }
    }
  }

  async testConfigurationFiles() {
    const requiredConfigs = [
      'tsconfig.json',
      '.env.example'
    ];

    for (const config of requiredConfigs) {
      if (!fs.existsSync(config)) {
        throw new Error(`Required configuration file missing: ${config}`);
      }
    }
  }

  async testDocumentation() {
    const requiredDocs = [
      'README.md',
      'QUICKSTART.md',
      'CHANGELOG.md',
      'PROJECT_SUMMARY.md'
    ];

    for (const doc of requiredDocs) {
      if (!fs.existsSync(doc)) {
        throw new Error(`Required documentation missing: ${doc}`);
      }
    }
  }

  async testCoreComponents() {
    const coreDir = 'src/core';
    const requiredComponents = [
      'executor.ts',
      'error-detector.ts',
      'diff-reviewer.ts'
    ];

    for (const component of requiredComponents) {
      const componentPath = path.join(coreDir, component);
      if (!fs.existsSync(componentPath)) {
        throw new Error(`Required core component missing: ${component}`);
      }
    }
  }

  async runAllTests() {
    console.log(colors.cyan('\n🧪 Running Enhanced Features Test Suite\n'));
    console.log(colors.gray('Testing all newly implemented features and enhancements...\n'));

    await this.runTest('Build Status', () => this.testBuildStatus());
    await this.runTest('File Structure', () => this.testFileStructure());
    await this.runTest('Enhanced CLI Interface', () => this.testEnhancedCLIInterface());
    await this.runTest('Tools Implementation', () => this.testToolsImplementation());
    await this.runTest('Model Providers', () => this.testModelProviders());
    await this.runTest('Type Definitions', () => this.testTypeDefinitions());
    await this.runTest('Package Configuration', () => this.testPackageConfiguration());
    await this.runTest('Configuration Files', () => this.testConfigurationFiles());
    await this.runTest('Documentation', () => this.testDocumentation());
    await this.runTest('Core Components', () => this.testCoreComponents());

    this.displayResults();
  }

  displayResults() {
    console.log(colors.cyan('\n📊 Test Results Summary\n'));

    this.testResults.forEach(result => {
      const status = result.status === 'PASSED' ?
        colors.green('✅ PASSED') :
        colors.red('❌ FAILED');

      console.log(`${status} - ${result.name}`);

      if (result.error) {
        console.log(colors.red(`   Error: ${result.error}`));
      }
    });

    console.log(colors.cyan(`\n📈 Summary: ${this.passedTests} passed, ${this.failedTests} failed\n`));

    if (this.failedTests === 0) {
      console.log(colors.green('🎉 All enhanced features are working correctly!'));
      console.log(colors.cyan('✨ Kritrima AI is ready for use with all enhancements.'));
    } else {
      console.log(colors.yellow('⚠️  Some tests failed. Please review the errors above.'));
    }
  }
}

// Run the tests
const tester = new EnhancedFeaturesTest();
tester.runAllTests().catch(error => {
  console.error(colors.red(`Test suite failed: ${error.message}`));
  process.exit(1);
});
